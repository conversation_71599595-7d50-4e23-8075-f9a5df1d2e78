import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Plus } from "lucide-react";
import { User, UserRole } from "@/types/project";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface CreateProjectDialogProps {
  onCreateProject: (project: { title: string; client: string; salesAmount: number }) => void;
  currentUser: User;
  users: User[];
}

export const CreateProjectDialog = ({ onCreateProject, currentUser, users }: CreateProjectDialogProps) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    client: '',
    salesAmount: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.client || !formData.salesAmount) {
      return; // Required fields; UI already uses required attributes
    }

    onCreateProject({
      title: formData.title,
      client: formData.client,
      salesAmount: parseInt(formData.salesAmount, 10),
    });

    setFormData({
      title: '',
      client: '',
      salesAmount: ''
    });
    setOpen(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          New Project
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Project Title</Label>
            <Input
              id="title"
              placeholder="Enter project title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="client">Client Name</Label>
            <Input
              id="client"
              placeholder="Enter client name"
              value={formData.client}
              onChange={(e) => handleInputChange('client', e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="salesAmount">Sales Amount ($)</Label>
            <Input
              id="salesAmount"
              type="number"
              placeholder="Enter total sales amount"
              value={formData.salesAmount}
              onChange={(e) => handleInputChange('salesAmount', e.target.value)}
              required
            />
          </div>

          {/* Sales assignment removed - will be handled by manager after creation */}

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit">Create Project</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};