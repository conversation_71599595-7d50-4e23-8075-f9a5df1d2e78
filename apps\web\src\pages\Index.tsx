import { useEffect, useState } from "react";
import {
  Project,
  ProjectStatus,
  UserRole,
  SalesWonSubStatus,
  SALES_WON_SUB_LABELS,
  getStatusLabelsForRole,
  getStatusesForRole,
  getBaseRole,
  SalesStatus,
  DesignerStatus,
  SupervisorStatus
} from "@/types/project";
// import { calculateDueDate } from "@/utils/dueDateCalculator"; // moved server-side
import { WorkflowHeader } from "@/components/WorkflowHeader";
import { ProjectCard } from "@/components/ProjectCard";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Filter, Grid, List, Archive, Plus } from "lucide-react";
import { Link } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useUserContext } from "@/contexts/UserContext";
// import { AssigneeFilter } from "@/components/AssigneeFilter"; // TODO: Re-enable with user management
import { TaskSorting, SortOption } from "@/components/TaskSorting";
import { ProjectDetailsDialog } from "@/components/ProjectDetailsDialog";
import { CreateProjectDialog } from "@/components/CreateProjectDialog";

// Role classification to disambiguate statuses like 'completed'
const SALES_STATUSES = ['sales_pending_assign','lead','quotation','potential','won_deal','lost_deal','completed'] as const;
const DESIGNER_STATUSES = ['designer_pending_assign','checklist','3d','2d','furniture_list','complete','designer_lost_deal'] as const;
const SUPERVISOR_STATUSES = ['supervisor_pending_assign','inprogress','completed','supervisor_lost_deal'] as const;

const classifyRoleForProject = (p: Project): 'sales' | 'designer' | 'supervisor' => {
  if ((SALES_STATUSES as readonly string[]).includes(p.status as string)) return 'sales';
  if ((DESIGNER_STATUSES as readonly string[]).includes(p.status as string)) return 'designer';
  if ((SUPERVISOR_STATUSES as readonly string[]).includes(p.status as string)) return 'supervisor';
  if (p.status === 'complete') return 'designer';
  if (!p.parentTaskId) return 'sales';
  return 'sales';
};

// TODO: Remove mock data once Clerk integration is complete

// TODO: Remove mock data - using real API data now
/* const mockProjects: Project[] = [
  {
    id: '1',
    title: 'Modern Office Renovation',
    client: 'Tech Corp Inc.',
    status: 'quotation' as SalesStatus,
    createdAt: '2024-01-15',
    updatedAt: '2024-01-15',
    assignedTo: '1', // Sarah Johnson ID
    salesAmount: 85000
  },
  {
    id: '2',
    title: 'Residential Kitchen Remodel',
    client: 'The Smith Family',
    status: 'won_deal' as SalesStatus,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-10',
    assignedTo: '1', // Sarah Johnson ID
    salesAmount: 75000,
    salesSubStatus: '5%' as SalesWonSubStatus,
    remarks: 'Client requested premium finishes and extended timeline for quality work.'
  },
  {
    id: '3',
    title: 'Retail Store Buildout',
    client: 'Fashion Boutique LLC',
    status: '3d' as DesignerStatus,
    createdAt: '2024-01-08',
    updatedAt: '2024-01-08',
    assignedTo: '2', // Mike Chen ID
    salesAmount: 120000,
    dueDate: '2024-01-20',
    expiredDate: '2024-01-18',
    remarks: 'Focus on modern aesthetic with sustainable materials. Client very particular about lighting design.'
  },
  {
    id: '4',
    title: 'Conference Room Upgrade',
    client: 'Law Firm Partners',
    status: 'plaster_ceiling' as SupervisorStatus,
    createdAt: '2024-01-05',
    updatedAt: '2024-01-05',
    assignedTo: '3', // David Rodriguez ID
    salesAmount: 65000
  },
  {
    id: '5',
    title: 'Warehouse Renovation',
    client: 'Storage Solutions Inc',
    status: 'carpentry_install' as SupervisorStatus,
    createdAt: '2024-01-12',
    updatedAt: '2024-01-12',
    assignedTo: '4', // Lisa Wang ID (manager)
    salesAmount: 95000
  },
  {
    id: '6',
    title: 'Corporate Office Design',
    client: 'Business Corp Ltd',
    status: 'designer_pending_assign' as DesignerStatus,
    createdAt: '2024-01-14',
    updatedAt: '2024-01-14',
    assignedTo: '', // Unassigned pending task
    salesAmount: 105000,
    remarks: 'Rush project - client needs completion by end of month for grand opening.'
  }
]; */

import { Api } from "@/lib/api";

const Index = () => {
  const { user: currentUser } = useUserContext();
  const [currentView, setCurrentView] = useState<UserRole>('sales'); // Separate view state
  const [projects, setProjects] = useState<Project[]>([]);
  const [users, setUsers] = useState<Array<{id: string; name: string; email: string; role: string}>>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [statusFilter, setStatusFilter] = useState<ProjectStatus | 'all'>('all');
  const [subStatusFilter, setSubStatusFilter] = useState<SalesWonSubStatus | 'all'>('all');

  // TODO: Remove API auth setup - handled by Clerk automatically
  useEffect(() => {
    if (currentUser) {
      Api.setAuth(currentUser.id, currentUser.role); // Deprecated but kept for compatibility
    }
  }, [currentUser]);
  // const [assigneeFilter, setAssigneeFilter] = useState<string>('all'); // TODO: Re-enable with user management
  const [sortBy, setSortBy] = useState<SortOption>('created_date_desc');

  useEffect(() => {
    Api.listProjects()
      .then(setProjects)
      .catch(() => toast({ title: 'Error', description: 'Failed to load projects', variant: 'destructive' } as Parameters<typeof toast>[0]));
  }, []);

  useEffect(() => {
    // Only fetch users if current user is manager or admin (has permission)
    if (currentUser && (currentUser.role === 'manager' || currentUser.role === 'admin')) {
      Api.listUsers()
        .then(setUsers)
        .catch(console.error);
    }
  }, [currentUser]);

  const { toast } = useToast();

  // Authentication and loading are handled by ProtectedRoute wrapper
  // This component only renders when user is authenticated and loaded
  if (!currentUser) {
    return null; // Should never happen due to ProtectedRoute, but satisfies TypeScript
  }

  const handleRoleChange = (role: UserRole) => {
    // Just change the view, not the user
    setCurrentView(role);
  };

  const handleCreateProject = async (projectData: { title: string; client: string; salesAmount: number }) => {
    try {
      const created = await Api.createProject({
        title: projectData.title,
        client: projectData.client,
        salesAmount: projectData.salesAmount,
      });
      setProjects(prev => [created, ...prev]);
      toast({ title: "Project Created", description: `${created.title} has been created successfully. It's now pending sales assignment.` });
    } catch (e) {
      toast({ title: 'Error creating project', description: String(e), variant: 'destructive' } as Parameters<typeof toast>[0]);
    }
  };

  const handleStatusUpdate = async (
    projectId: string,
    newStatus: ProjectStatus,
    newSubStatus?: SalesWonSubStatus,
    phaseCompletedAt?: string,
    phaseKey?: string
  ) => {
    try {
      const updated = await Api.updateProjectStatus(projectId, {
        status: newStatus,
        salesSubStatus: newSubStatus,
        phaseCompletedAt,
        phaseKey,
        isPhaseCompletion: !!phaseCompletedAt // If phaseCompletedAt is provided, this is a phase completion
      });
      setProjects(prev => prev.map(p => (p.id === projectId ? updated : p)));
      // Refresh list to include any server-created follow-up tasks (e.g., Designer pending_assign)
      const fresh = await Api.listProjects();
      setProjects(fresh);
    } catch (e) {
      toast({ title: 'Error updating status', description: String(e), variant: 'destructive' } as Parameters<typeof toast>[0]);
      return;
    }

    const project = projects.find(p => p.id === projectId);
    if (newSubStatus) {
      toast({
        title: "Sub-status Updated",
        description: `${project?.title} updated to ${SALES_WON_SUB_LABELS[newSubStatus]}.`
      });
    } else {
      toast({
        title: "Status Updated",
        description: `${project?.title} status updated.`
      });
    }
  };

  const handleRevisionRequest = async (projectId: string) => {
    const currentTime = new Date().toISOString();
    const project = projects.find(p => p.id === projectId);

    if (!project) return;

    try {
      const updated = await Api.updateProjectStatus(projectId, { status: project.status, isRevision: true });
      setProjects(prev => prev.map(p => (p.id === projectId ? updated : p)));
    } catch (e) {
      toast({ title: 'Error requesting revision', description: String(e), variant: 'destructive' } as Parameters<typeof toast>[0]);
      return;
    }

    toast({
      title: "Revision Requested",
      description: `A new ${project.status.toUpperCase()} revision has been added to the project timeline.`
    });
  };

  // Helper functions to determine project role (matching API logic)
  const isSalesTask = (project: Project): boolean => !project.parentTaskId;
  const isDesignerTask = (project: Project): boolean =>
    !!project.parentTaskId && ['designer_pending_assign','checklist','3d','2d','furniture_list','complete','designer_lost_deal'].includes(project.status);
  const isSupervisorTask = (project: Project): boolean =>
    ['supervisor_pending_assign','inprogress','completed','supervisor_lost_deal'].includes(project.status);

  const getCorrectLostDealStatus = (project: Project): ProjectStatus => {
    if (isSalesTask(project)) return 'lost_deal';
    if (isDesignerTask(project)) return 'designer_lost_deal';
    if (isSupervisorTask(project)) return 'supervisor_lost_deal';
    // Fallback to generic lost_deal if role cannot be determined
    return 'lost_deal';
  };

  const handleDeleteProject = async (projectId: string) => {
    try {
      const project = projects.find(p => p.id === projectId);
      if (!project) {
        toast({ title: 'Error', description: 'Project not found', variant: 'destructive' } as Parameters<typeof toast>[0]);
        return;
      }

      const correctLostDealStatus = getCorrectLostDealStatus(project);
      const updated = await Api.updateProjectStatus(projectId, { status: correctLostDealStatus });
      setProjects(prev => prev.map(p => (p.id === projectId ? updated : p)));
      // Refresh list to ensure client state matches backend
      const fresh = await Api.listProjects();
      setProjects(fresh);

      // Close the details dialog if the deleted project is currently selected
      if (selectedProject && selectedProject.id === projectId) {
        setSelectedProject(null);
      }

      toast({
        title: "Project Deleted",
        description: `${project.title} has been moved to Lost Deal status.`
      });
    } catch (e) {
      toast({ title: 'Error deleting task', description: String(e), variant: 'destructive' } as Parameters<typeof toast>[0]);
    }
  };

  const handleAssignTask = async (projectId: string, assigneeId: string, phases?: string[]) => {
    try {
      let updated: Project;
      const project = projects.find(p => p.id === projectId);
      const status = project?.status as string;
      const isDesignerTask = ['designer_pending_assign','checklist','3d','2d','furniture_list','complete'].includes(status);
      const isSupervisorTask = phases && phases.length > 0;
      const isSalesTask = status === 'sales_pending_assign';

      console.log('DEBUG Frontend Assignment:', { projectId, status, isDesignerTask, isSupervisorTask, isSalesTask, phases });

      if (isSupervisorTask) {
        console.log('Calling supervisor assignment API');
        updated = await Api.assignSupervisor(projectId, assigneeId, phases!);
      } else if (isDesignerTask) {
        console.log('Calling designer assignment API');
        updated = await Api.assignDesigner(projectId, assigneeId);
      } else if (isSalesTask) {
        console.log('Calling sales assignment API');
        // Sales assignment from pending assign
        updated = await Api.assignSales(projectId, assigneeId);
      } else {
        console.log('Calling general assignment API (fallback)');
        // General assignment (fallback)
        updated = await Api.updateProject(projectId, { assignedTo: assigneeId });
      }
      setProjects(prev => prev.map(p => (p.id === projectId ? updated : p)));
      // TODO: Get user name from user management system
      toast({ title: "Task Assigned", description: `Task has been assigned.` });
    } catch (e) {
      toast({ title: 'Error assigning task', description: String(e), variant: 'destructive' } as Parameters<typeof toast>[0]);
    }
  };

  const handleUpdateExpiredDate = async (projectId: string, newExpiredDate: string) => {
    try {
      const updated = await Api.updateProject(projectId, { expiredDate: newExpiredDate });
      setProjects(prev => prev.map(p => (p.id === projectId ? updated : p)));
    } catch (e) {
      toast({ title: 'Error updating expired date', description: String(e), variant: 'destructive' } as Parameters<typeof toast>[0]);
    }
  };

  const handleUpdateDueDate = async (projectId: string, newDueDate: string): Promise<Project> => {
    try {
      const updated = await Api.updateProject(projectId, { dueDate: newDueDate });
      setProjects(prev => prev.map(p => (p.id === projectId ? updated : p)));
      return updated;
    } catch (e) {
      toast({ title: 'Error updating due date', description: String(e), variant: 'destructive' } as Parameters<typeof toast>[0]);
      throw e;
    }
  };

  const handleUpdateRemarks = async (projectId: string, remarks: string): Promise<Project> => {
    try {
      const updated = await Api.updateProject(projectId, { remarks });
      setProjects(prev => prev.map(p => (p.id === projectId ? updated : p)));
      return updated;
    } catch (e) {
      toast({ title: 'Error updating remarks', description: String(e), variant: 'destructive' } as Parameters<typeof toast>[0]);
      throw e;
    }
  };

  // Case-related helpers with 7-day hide rules
  const getRelatedProjects = (p: Project): Project[] => {
    if (p.caseId) return projects.filter(x => x.caseId === p.caseId);
    return projects.filter(x => x.title === p.title && x.client === p.client);
  };
  const toDate = (s?: string) => (s ? new Date(s) : undefined);
  const getUpdatedAt = (p: Project) => toDate(p.updatedAt) ?? toDate(p.createdAt);
  const daysSince = (d?: Date) => (d ? (Date.now() - d.getTime()) / (1000 * 60 * 60 * 24) : Infinity);

  const getCaseCompletionInfo = (related: Project[]) => {
    const salesDone = related.filter(r => classifyRoleForProject(r) === 'sales' && r.status === 'completed');
    const designerDone = related.filter(r => classifyRoleForProject(r) === 'designer' && r.status === 'complete');
    const supervisorDone = related.filter(r => classifyRoleForProject(r) === 'supervisor' && r.status === 'completed');
    const completed = salesDone.length > 0 && designerDone.length > 0 && supervisorDone.length > 0;
    let completedAt: Date | undefined = undefined;
    if (completed) {
      const dates: Date[] = [];
      [salesDone, designerDone, supervisorDone].forEach(arr => {
        arr.forEach(r => { const d = getUpdatedAt(r); if (d) dates.push(d); });
      });
      if (dates.length) completedAt = new Date(Math.max(...dates.map(d => d.getTime())));
    }
    return { completed, completedAt } as const;
  };

  const filteredProjects = projects.filter(project => {
    const related = getRelatedProjects(project);

    // Lost deal: hide only after 7 days since the latest lost_deal update in this case
    const lost = related.filter(p => ['lost_deal', 'designer_lost_deal', 'supervisor_lost_deal'].includes(p.status));
    if (lost.length > 0) {
      const lastLostAt = new Date(Math.max(...lost.map(p => (getUpdatedAt(p)?.getTime() ?? 0))));
      if (daysSince(lastLostAt) > 7) return false;
    }

    // Completed case (all roles done): hide only after 7 days since completion
    const { completed, completedAt } = getCaseCompletionInfo(related);
    if (completed && daysSince(completedAt) > 7) {
      return false;
    }

    // TODO: Re-enable assignee filter with user management
    // if (assigneeFilter !== 'all' && project.assignedTo !== assigneeFilter) {
    //   return false;
    // }

    if (statusFilter === 'all') {
      if (subStatusFilter === 'all') return true;
      return project.salesSubStatus === subStatusFilter;
    }
    if (statusFilter === 'won_deal' && subStatusFilter !== 'all') {
      return project.status === statusFilter && project.salesSubStatus === subStatusFilter;
    }
    return project.status === statusFilter;
  });

  // Filter projects based on current VIEW (not user role)
  const getProjectsForView = () => {
    const role = currentView; // Use currentView instead of currentUser.role
    const roleStatuses = getStatusesForRole(role);
    const baseRole = getBaseRole(role);
    const managerRole: UserRole = 'manager';

    return filteredProjects.filter(p => {
      // Must match the statuses for the current view's base role
      if (!roleStatuses.includes(p.status)) return false;

      // Disambiguate 'completed': supervisor completed tasks should not appear in Sales board
      if (p.status === 'completed' && baseRole === 'sales' && p.parentTaskId) return false;

      // Disambiguate 'completed': sales completed (root) should not appear in Supervisor board
      if (p.status === 'completed' && baseRole === 'supervisor' && !p.parentTaskId) return false;

      // Everyone sees the same tasks within the role family's board
      return true;
    });
  };

  const sortProjects = (projects: Project[]): Project[] => {
    return [...projects].sort((a, b) => {
      switch (sortBy) {
        case 'created_date_desc':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'created_date_asc':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'due_date_asc':
          if (!a.dueDate && !b.dueDate) return 0;
          if (!a.dueDate) return 1;
          if (!b.dueDate) return -1;
          return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        case 'due_date_desc':
          if (!a.dueDate && !b.dueDate) return 0;
          if (!a.dueDate) return 1;
          if (!b.dueDate) return -1;
          return new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();
        case 'title_asc':
          return a.title.localeCompare(b.title);
        case 'title_desc':
          return b.title.localeCompare(a.title);
        case 'client_asc':
          return a.client.localeCompare(b.client);
        case 'client_desc':
          return b.client.localeCompare(a.client);
        default:
          return 0;
      }
    });
  };

  const relevantProjects = sortProjects(getProjectsForView());

  // Get role-specific statuses for tabs (based on current view)
  const roleStatuses = getStatusesForRole(currentView);
  const statusLabels = getStatusLabelsForRole(currentView);

  return (
    <div className="min-h-screen bg-background">
      <WorkflowHeader
        currentUser={currentUser}
        currentView={currentView}
        onRoleChange={handleRoleChange}
      />

      <div className="container mx-auto px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold">Projects Dashboard</h2>
            <p className="text-muted-foreground">
              Showing {relevantProjects.length} projects for {currentView} view
            </p>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 px-3 py-2 bg-muted/50 rounded-lg border">
              <Filter className="h-4 w-4 text-muted-foreground" />
              {/* TODO: Re-enable AssigneeFilter with user management system */}
              <span className="text-sm text-muted-foreground">All Assignees</span>

              {currentView === 'supervisor' && (
                <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as SupervisorStatus | 'all')}>
                  <SelectTrigger className="w-48 border-0 bg-transparent shadow-none">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Tasks</SelectItem>
                    <SelectItem value="floor_protection">Floor Protection</SelectItem>
                    <SelectItem value="plaster_ceiling">Plaster Ceiling</SelectItem>
                    <SelectItem value="spc">SPC</SelectItem>
                    <SelectItem value="first_painting">First Painting</SelectItem>
                    <SelectItem value="carpentry_measure">Carpentry Measure</SelectItem>
                    <SelectItem value="measure_others">Measure Others</SelectItem>
                    <SelectItem value="carpentry_install">Carpentry Install</SelectItem>
                    <SelectItem value="quartz_measure">Quartz Measure</SelectItem>
                    <SelectItem value="quartz_install">Quartz Install</SelectItem>
                    <SelectItem value="glass_measure">Glass Measure</SelectItem>
                    <SelectItem value="glass_install">Glass Install</SelectItem>
                    <SelectItem value="final_wiring">Final Wiring</SelectItem>
                    <SelectItem value="final_painting">Final Painting</SelectItem>
                    <SelectItem value="install_others">Install Others</SelectItem>
                    <SelectItem value="plumbing">Plumbing</SelectItem>
                    <SelectItem value="cleaning">Cleaning</SelectItem>
                    <SelectItem value="defects">Defects</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>

            <TaskSorting
              sortBy={sortBy}
              onSortChange={setSortBy}
            />

            <Link to="/case-history">
              <Button variant="outline" size="sm">
                <Archive className="h-4 w-4 mr-2" />
                Case History
              </Button>
            </Link>

            {currentUser && (currentUser.role === 'sales' || currentUser.role === 'manager' || currentUser.role === 'admin') && (
              <CreateProjectDialog
                onCreateProject={handleCreateProject}
                currentUser={currentUser}
                users={users}
              />
            )}

            <div className="flex items-center gap-1 bg-muted p-1 rounded-lg">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {currentView !== 'supervisor' && (
          <Tabs value={statusFilter} onValueChange={(value) => setStatusFilter(value as ProjectStatus | 'all')} className="mb-6">
            <TabsList className="w-full flex flex-wrap gap-2 justify-between">
              <TabsTrigger value="all" className="flex-1">All</TabsTrigger>
              {roleStatuses.map((status) => (
                <TabsTrigger key={status} value={status} className="flex-1">
                  {statusLabels[status as keyof typeof statusLabels]}
                  {status === 'won_deal' && currentView === 'sales' && (
                    <div className="text-xs text-muted-foreground ml-1">
                      ({filteredProjects.filter(p => p.status === 'won_deal').length})
                    </div>
                  )}
                </TabsTrigger>
              ))}
            </TabsList>

            {/* Won Deal Sub-status tabs */}
            {currentView === 'sales' && statusFilter === 'won_deal' && (
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h4 className="text-sm font-medium mb-2">Won Deal Sub-Status</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge
                    key="all"
                    variant={subStatusFilter === 'all' ? 'default' : 'outline'}
                    className="text-xs cursor-pointer hover:bg-primary/10"
                    onClick={() => setSubStatusFilter('all')}
                  >
                    All
                  </Badge>
                  {['10%', '5%', '45%', '37%', '3%'].map((subStatus) => (
                    <Badge
                      key={subStatus}
                      variant={subStatusFilter === subStatus ? 'default' : 'outline'}
                      className="text-xs cursor-pointer hover:bg-primary/10"
                      onClick={() => setSubStatusFilter(subStatus as SalesWonSubStatus)}
                    >
                      {SALES_WON_SUB_LABELS[subStatus as SalesWonSubStatus]}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </Tabs>
        )}

        {relevantProjects.length === 0 ? (
          <div className="text-center py-12">
            <Filter className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No projects found</h3>
            <p className="text-muted-foreground">
              {currentView === 'sales'
                ? "No sales projects found with current filters."
                : `No projects assigned to ${currentView} role at the moment.`}
            </p>
          </div>
        ) : (
          <div className={viewMode === 'grid'
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
          }>
            {relevantProjects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                userRole={currentUser.role}
                currentUserId={currentUser.id}
                onStatusUpdate={handleStatusUpdate}
                onViewDetails={setSelectedProject}
                onRevisionRequest={handleRevisionRequest}
                onDeleteProject={handleDeleteProject}
                onAssignTask={handleAssignTask}
                availableUsers={users}
              />
            ))}
          </div>
        )}
      </div>

      {/* Project Details Dialog */}
      <ProjectDetailsDialog
        project={selectedProject}
        isOpen={!!selectedProject}
        onClose={() => setSelectedProject(null)}
        currentUserRole={currentUser.role}
        currentUserId={currentUser.id}
        onUpdateExpiredDate={handleUpdateExpiredDate}
        onUpdateDueDate={handleUpdateDueDate}
        onAssignTask={handleAssignTask}
        onDeleteProject={handleDeleteProject}
        onUpdateRemarks={handleUpdateRemarks}
        users={users}
      />
    </div>
  );
};

export default Index;
