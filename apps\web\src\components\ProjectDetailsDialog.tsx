import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Project, UserRole, isManager, canEditProject, canDeleteProject, SUPERVISOR_STATUS_LABELS, SUPERVISOR_SUBTASK_LABELS, SupervisorStatus, SupervisorSubTask, User } from "@/types/project";
import { Calendar, Save, Edit2, X, Trash2, User as UserIcon } from "lucide-react";
import { toast } from "sonner";
import { formatDate, formatDateTime, formatCurrency, formatDateToISO } from "@/lib/datetime";
import { StatusBadge } from "@/components/StatusBadge";
import { shouldHideActionButtons } from "@/lib/status";

// Phase status types from SupervisorPhaseDialog
type PhaseStatus = 'not_started' | 'booked' | 'pending' | 'in_progress' | 'complete' | 'defect';
type PhaseStateRecord = Record<string, { status: PhaseStatus; updatedAt?: string }>;

const STATUS_LABEL: Record<PhaseStatus, string> = {
  not_started: 'Not Started',
  booked: 'Booked',
  pending: 'Pending',
  in_progress: 'In Progress',
  complete: 'Complete',
  defect: 'Defect',
};

const STATUS_TONE: Record<PhaseStatus, string> = {
  not_started: 'bg-muted text-muted-foreground',
  booked: 'bg-blue-100 text-blue-700 dark:bg-blue-950 dark:text-blue-300',
  pending: 'bg-amber-100 text-amber-700 dark:bg-amber-950 dark:text-amber-300',
  in_progress: 'bg-sky-100 text-sky-700 dark:bg-sky-950 dark:text-sky-300',
  complete: 'bg-emerald-100 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-300',
  defect: 'bg-rose-100 text-rose-700 dark:bg-rose-950 dark:text-rose-300',
};
import { getDueDateInfo } from "@/utils/dueDateCalculator";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SupervisorAssignInline } from "@/components/SupervisorAssignInline";

interface ProjectDetailsDialogProps {
  project: Project | null;
  isOpen: boolean;
  onClose: () => void;
  currentUserRole: UserRole;
  currentUserId: string;
  onUpdateExpiredDate?: (projectId: string, newExpiredDate: string) => void;
  onUpdateDueDate?: (projectId: string, newDueDate: string) => Promise<Project>;
  onAssignTask?: (projectId: string, assigneeId: string, phases?: string[]) => void;
  onDeleteProject?: (projectId: string) => void;
  onUpdateRemarks?: (projectId: string, remarks: string) => Promise<Project>;
  users?: User[];
}

export const ProjectDetailsDialog = ({
  project,
  isOpen,
  onClose,
  currentUserRole,
  currentUserId,
  onUpdateExpiredDate,
  onUpdateDueDate,
  onAssignTask,
  onDeleteProject,
  onUpdateRemarks,
  users = []
}: ProjectDetailsDialogProps) => {
  const [isEditingExpiredDate, setIsEditingExpiredDate] = useState(false);
  const [newExpiredDate, setNewExpiredDate] = useState('');
  const [isEditingDueDate, setIsEditingDueDate] = useState(false);
  const [newDueDate, setNewDueDate] = useState('');
  const [isEditingRemarks, setIsEditingRemarks] = useState(false);
  const [newRemarks, setNewRemarks] = useState('');
  const [localProject, setLocalProject] = useState<Project | null>(project);
  const [showCloseConfirmation, setShowCloseConfirmation] = useState(false);

  // Sync local project state when prop changes
  useEffect(() => {
    setLocalProject(project);
  }, [project]);

  // Check if any edit mode is active
  const isAnyEditModeActive = isEditingExpiredDate || isEditingDueDate || isEditingRemarks;

  // Handle dialog close with confirmation if in edit mode
  const handleDialogClose = (open: boolean) => {
    if (!open && isAnyEditModeActive) {
      setShowCloseConfirmation(true);
    } else if (!open) {
      onClose();
    }
  };

  // Confirm close and discard changes
  const handleConfirmClose = () => {
    // Reset all edit states and clear temporary data
    setIsEditingExpiredDate(false);
    setIsEditingDueDate(false);
    setIsEditingRemarks(false);
    setNewExpiredDate('');
    setNewDueDate('');
    setNewRemarks('');
    setShowCloseConfirmation(false);
    onClose();
  };

  // Cancel close confirmation
  const handleCancelClose = () => {
    setShowCloseConfirmation(false);
  };

  if (!project || !localProject) return null;

  // Helper function to get due date for each revision
  // This ensures the current active revision shows the manager-updated due date,
  // while older revisions show their original calculated due dates
  const getRevisionDueDate = (revisionDate: string, revisionType: '3d' | '2d', revisionIndex: number, salesAmount?: number): string => {
    if (!salesAmount) return '';

    // Check if this is the current active revision
    const currentRevisions = revisionType === '3d' ? (localProject.revisions3d || []) : (localProject.revisions2d || []);
    const isCurrentRevision = revisionIndex === currentRevisions.length - 1;
    const isCurrentStatus = localProject.status === revisionType;

    // If this is the current active revision and status matches, use the main dueDate
    // (which reflects any manager updates and matches the upper section)
    if (isCurrentRevision && isCurrentStatus && localProject.dueDate) {
      return localProject.dueDate;
    }

    // For older revisions, calculate based on the original algorithm
    const dueDateInfo = getDueDateInfo(revisionType, salesAmount);
    const revisionDateTime = new Date(revisionDate);
    const dueDate = new Date(revisionDateTime);
    dueDate.setDate(revisionDateTime.getDate() + dueDateInfo.revision);

    return dueDate.toISOString();
  };
  const canEditExpiredDate = isManager(currentUserRole) && canEditProject(currentUserRole);
  const canEditDueDate = isManager(currentUserRole) && canEditProject(currentUserRole);

  const handleEditExpiredDate = () => {
    setNewExpiredDate(localProject.expiredDate || '');
    setIsEditingExpiredDate(true);
  };

  const handleSaveExpiredDate = () => {
    if (newExpiredDate && onUpdateExpiredDate) {
      onUpdateExpiredDate(localProject.id, newExpiredDate);
      setIsEditingExpiredDate(false);
      toast.success("Expired date updated successfully");
    }
  };

  const handleCancelEdit = () => {
    setIsEditingExpiredDate(false);
    setNewExpiredDate('');
  };

  const handleEditRemarks = () => {
    setNewRemarks(localProject.remarks || '');
    setIsEditingRemarks(true);
  };

  const handleSaveRemarks = async () => {
    if (onUpdateRemarks) {
      try {
        const updatedProject = await onUpdateRemarks(localProject.id, newRemarks);
        setLocalProject(updatedProject);
        setIsEditingRemarks(false);
        toast.success("Remarks updated successfully");
      } catch (error) {
        toast.error("Failed to update remarks");
      }
    }
  };

  const handleCancelRemarksEdit = () => {
    setIsEditingRemarks(false);
    setNewRemarks('');
  };

  const getCurrentStageInfo = () => {
    if (localProject.status === '3d') {
      const revisionCount = localProject.revisions3d?.length || 0;
      if (revisionCount === 0) {
        return { stage: '3D', revision: 'First Draft', key: '3d-draft' };
      } else {
        return { stage: '3D', revision: `Revision ${revisionCount}`, key: `3d-rev-${revisionCount}` };
      }
    } else if (localProject.status === '2d') {
      const revisionCount = localProject.revisions2d?.length || 0;
      if (revisionCount === 0) {
        return { stage: '2D', revision: 'First Draft', key: '2d-draft' };
      } else {
        return { stage: '2D', revision: `Revision ${revisionCount}`, key: `2d-rev-${revisionCount}` };
      }
    }
    return { stage: localProject.status.toUpperCase(), revision: 'Current', key: localProject.status };
  };

  const handleEditDueDate = () => {
    // Use the utility function to ensure consistent date formatting
    setNewDueDate(formatDateToISO(localProject.dueDate));
    setIsEditingDueDate(true);
  };

  const handleSaveDueDate = async () => {
    if (newDueDate && onUpdateDueDate) {
      const stageInfo = getCurrentStageInfo();
      try {
        const updatedProject = await onUpdateDueDate(localProject.id, newDueDate);
        setLocalProject(updatedProject); // Update local state immediately
        setIsEditingDueDate(false);
        toast.success(`Due date updated for ${stageInfo.stage} - ${stageInfo.revision}`);
      } catch (error) {
        toast.error('Failed to update due date');
      }
    }
  };

  const handleCancelDueDateEdit = () => {
    setIsEditingDueDate(false);
    setNewDueDate('');
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>{localProject.title}</DialogTitle>
          </DialogHeader>
        <div className="space-y-4 overflow-y-auto flex-1 pr-2">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Client</label>
              <p>{localProject.client}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Status</label>
              <div className="mt-1">
                <StatusBadge status={localProject.status} />
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Created</label>
              <p>{formatDate(localProject.createdAt)}</p>
            </div>
            {localProject.salesAmount && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Sales Amount</label>
                <p>{formatCurrency(localProject.salesAmount)}</p>
              </div>
            )}
            {localProject.dueDate && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Due Date
                  {(localProject.status === '3d' || localProject.status === '2d') && (
                    <span className="ml-1 text-xs text-blue-600">
                      ({getCurrentStageInfo().stage} - {getCurrentStageInfo().revision})
                    </span>
                  )}
                </label>
                <div className="flex items-center gap-2">
                  {isEditingDueDate ? (
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <Input
                          type="date"
                          value={newDueDate}
                          onChange={(e) => setNewDueDate(e.target.value)}
                          className="w-auto"
                        />
                        <Button size="sm" onClick={handleSaveDueDate}>
                          <Save className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={handleCancelDueDateEdit}>
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Updating due date for: {getCurrentStageInfo().stage} - {getCurrentStageInfo().revision}
                      </p>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <p>{formatDate(localProject.dueDate)}</p>
                      {canEditDueDate && (
                        <Button size="sm" variant="ghost" onClick={handleEditDueDate}>
                          <Edit2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
            {localProject.expiredDate && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Expired Date</label>
                <div className="flex items-center gap-2">
                  {isEditingExpiredDate ? (
                    <div className="flex items-center gap-2">
                      <Input
                        type="date"
                        value={newExpiredDate}
                        onChange={(e) => setNewExpiredDate(e.target.value)}
                        className="w-auto"
                      />
                      <Button size="sm" onClick={handleSaveExpiredDate}>
                        <Save className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <p>{formatDate(localProject.expiredDate)}</p>
                      {canEditExpiredDate && (
                        <Button size="sm" variant="ghost" onClick={handleEditExpiredDate}>
                          <Edit2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Manager actions moved here: assign or delete, filtered by task role */}
          {isManager(currentUserRole) && !shouldHideActionButtons(project.status) && (
            <div className="border rounded p-3 bg-muted/30">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-muted-foreground">Manager Actions</span>
              </div>
              <div className="flex gap-2 flex-wrap">
                {/* Delete - only for users who can delete projects */}
                {canDeleteProject(currentUserRole) && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4 mr-1" /> Delete (Lost Deal)
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Delete</AlertDialogTitle>
                        <AlertDialogDescription>
                          Move this task to Lost Deal? It will be removed from the board but remain in Case History.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => onDeleteProject && onDeleteProject(project.id)}>Confirm</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}

                {/* Assign/Reassign filtered by task role */}
                {/* Assign/Reassign filtered by task role; for supervisors include phases */}
                {(['supervisor_pending_assign','inprogress','completed','floor_protection','plaster_ceiling','spc','first_painting','carpentry_measure','measure_others','carpentry_install','quartz_measure','quartz_install','glass_measure','glass_install','final_wiring','final_painting','install_others','plumbing','cleaning','defects'] as string[]).includes(project.status) ? (
                  <SupervisorAssignInline users={users} projectId={project.id} onAssign={onAssignTask} />
                ) : (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="default" size="sm">
                        <UserIcon className="h-4 w-4 mr-1" /> Assign / Reassign
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Assign Task</AlertDialogTitle>
                        <AlertDialogDescription>Select a user to assign this task to:</AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="py-2">
                        <Select onValueChange={(value) => onAssignTask && onAssignTask(project.id, value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select assignee" />
                          </SelectTrigger>
                          <SelectContent>
                            {users
                              .filter((u) => {
                                const s = project.status as string;
                                const isDesigner = ['designer_pending_assign','checklist','3d','2d','furniture_list','complete'].includes(s);
                                const isSales = ['sales_pending_assign','lead','quotation','potential','won_deal','lost_deal','completed'].includes(s);
                                if (isDesigner) return u.role === 'designer';
                                if (isSales) return u.role === 'sales';
                                return u.role === 'sales'; // fallback
                              })
                              .map((usr) => (
                                <SelectItem key={usr.id} value={usr.id}>
                                  {usr.name}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
              </div>
            </div>
          )}

          <div>
            <label className="text-sm font-medium text-muted-foreground">Project Type</label>
            <p className="mt-1">Project workflow task</p>
          </div>

          {project.completionProof && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Completion Notes</label>
              <p className="mt-1">{project.completionProof}</p>
            </div>
          )}

          {/* Remarks Section */}
          <div>
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-muted-foreground">Remarks</label>
              {!isEditingRemarks && canEditProject(currentUserRole) && (
                <Button variant="ghost" size="sm" onClick={handleEditRemarks}>
                  <Edit2 className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              )}
            </div>
            {isEditingRemarks ? (
              <div className="mt-2 space-y-2">
                <Textarea
                  value={newRemarks}
                  onChange={(e) => setNewRemarks(e.target.value)}
                  placeholder="Add remarks or notes about this project..."
                  className="min-h-[120px] max-h-[200px] resize-y"
                  rows={5}
                />
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleSaveRemarks}>
                    <Save className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleCancelRemarksEdit}>
                    <X className="h-4 w-4 mr-1" />
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div className="mt-1">
                {localProject.remarks ? (
                  <div className="text-sm whitespace-pre-wrap break-words max-w-full">
                    {localProject.remarks}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground italic">No remarks added</p>
                )}
              </div>
            )}
          </div>

          {!!(localProject.revisions3d?.length || localProject.revisions2d?.length) && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Revision History</label>
              <div className="mt-2 space-y-3">
                {localProject.revisions3d && localProject.revisions3d.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-blue-600 mb-2">3D Design Revisions</h4>
                    <div className="space-y-2">
                      {localProject.revisions3d.map((revision, index) => {
                        const revisionDueDate = getRevisionDueDate(revision, '3d', index, localProject.salesAmount);
                        return (
                          <div key={index} className="flex items-center justify-between p-2 border rounded">
                            <div className="flex items-center gap-2 text-sm">
                              <Badge variant="outline" className="text-xs bg-blue-50">
                                3D Rev {index + 1}
                              </Badge>
                              <span className="text-muted-foreground">
                                {formatDateTime(revision)}
                              </span>
                            </div>
                            {revisionDueDate && (
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <Calendar className="h-3 w-3" />
                                <span>Due: {formatDate(revisionDueDate)}</span>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                    {project.revisions3d.length > 3 && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-700 font-medium">
                          ⚠️ High 3D revision count ({project.revisions3d.length}) - Manager attention required
                        </p>
                      </div>
                    )}
                  </div>
                )}
                {localProject.revisions2d && localProject.revisions2d.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-green-600 mb-2">2D Design Revisions</h4>
                    <div className="space-y-2">
                      {localProject.revisions2d.map((revision, index) => {
                        const revisionDueDate = getRevisionDueDate(revision, '2d', index, localProject.salesAmount);
                        return (
                          <div key={index} className="flex items-center justify-between p-2 border rounded">
                            <div className="flex items-center gap-2 text-sm">
                              <Badge variant="outline" className="text-xs bg-green-50">
                                2D Rev {index + 1}
                              </Badge>
                              <span className="text-muted-foreground">
                                {formatDateTime(revision)}
                              </span>
                            </div>
                            {revisionDueDate && (
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <Calendar className="h-3 w-3" />
                                <span>Due: {formatDate(revisionDueDate)}</span>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                    {project.revisions2d.length > 3 && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-700 font-medium">
                          ⚠️ High 2D revision count ({project.revisions2d.length}) - Manager attention required
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Supervisor Phase Completion History */}
          {project.supervisorPhaseDates && Object.keys(project.supervisorPhaseDates).length > 0 && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Supervisor Phase Completion History</label>
              <div className="mt-2 space-y-2">
                {Object.entries(project.supervisorPhaseDates as Record<string, string>)
                  .sort(([, dateA], [, dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
                  .map(([phase, completionDate]) => (
                    <div key={phase} className="flex items-center justify-between p-3 border rounded-lg bg-gradient-to-r from-orange-50 to-green-50">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="text-xs bg-orange-100 text-orange-700 border-orange-200">
                          ✓ Completed
                        </Badge>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {SUPERVISOR_STATUS_LABELS[phase as SupervisorStatus] || phase}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Phase: {phase}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium">
                          {formatDateTime(completionDate)}
                        </span>
                      </div>
                    </div>
                  ))}
              </div>
              <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-700">
                  📋 Completion Summary: {Object.keys(project.supervisorPhaseDates).length > 0
                    ? `${Object.keys(project.supervisorPhaseDates).length} phase${Object.keys(project.supervisorPhaseDates).length > 1 ? 's' : ''} completed`
                    : 'No phases completed yet'
                  }
                  {project.supervisorSelectedPhases && project.supervisorSelectedPhases.length > 0 && (
                    <span className="ml-2">
                      | {project.supervisorSelectedPhases.length} phase{project.supervisorSelectedPhases.length > 1 ? 's' : ''} assigned
                    </span>
                  )}
                </p>
              </div>
            </div>
          )}

          {/* Supervisor Subtask Status Updates */}
          {((project.supervisorSelectedPhases && project.supervisorSelectedPhases.length > 0) ||
           (project.supervisorPhaseDates && Object.keys(project.supervisorPhaseDates).length > 0)) && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Subtask Status Updates</label>
              <div className="mt-2 space-y-2">
                {(project.supervisorSelectedPhases || []).map((phase) => {
                  // Get status from new phase states system or fallback to legacy
                  const phaseStates = (project as any).supervisorPhaseStates as PhaseStateRecord | undefined;
                  const phaseState = phaseStates?.[phase];
                  const currentStatus = phaseState?.status || 'not_started';
                  const isLegacyCompleted = project.supervisorPhaseDates && project.supervisorPhaseDates[phase];

                  // Use legacy completion for display if new system shows not_started but legacy shows completed
                  const displayStatus = (currentStatus === 'not_started' && isLegacyCompleted) ? 'complete' : currentStatus;
                  const lastModified = phaseState?.updatedAt || (isLegacyCompleted ? project.supervisorPhaseDates[phase] : null);

                  return (
                    <div key={phase} className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate" title={SUPERVISOR_SUBTASK_LABELS[phase as SupervisorSubTask]}>
                          {SUPERVISOR_SUBTASK_LABELS[phase as SupervisorSubTask] || phase}
                        </div>
                        {lastModified && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Last updated: {formatDateTime(lastModified)}
                          </div>
                        )}
                      </div>
                      <Badge className={`shrink-0 ${STATUS_TONE[displayStatus as PhaseStatus]}`}>
                        {STATUS_LABEL[displayStatus as PhaseStatus]}
                      </Badge>
                    </div>
                  );
                })}
              </div>
              {(!project.supervisorSelectedPhases || project.supervisorSelectedPhases.length === 0) &&
               (!project.supervisorPhaseDates || Object.keys(project.supervisorPhaseDates).length === 0) && (
                <div className="mt-2 p-3 bg-muted/30 border border-dashed rounded-lg">
                  <p className="text-sm text-muted-foreground text-center">No subtasks assigned yet</p>
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>

    {/* Close Confirmation Dialog */}
    <AlertDialog open={showCloseConfirmation} onOpenChange={setShowCloseConfirmation}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Unsaved Changes</AlertDialogTitle>
          <AlertDialogDescription>
            You have unsaved changes in edit mode. If you close this popup, all changes will be lost. Are you sure you want to continue?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancelClose}>
            Stay and Continue Editing
          </AlertDialogCancel>
          <AlertDialogAction onClick={handleConfirmClose} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
            Discard Changes and Close
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
    </>
  );
};